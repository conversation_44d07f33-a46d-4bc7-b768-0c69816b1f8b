package logic

import (
	"context"
	"errors"
	"loginsrv/internal/dao"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// TokenLogin Token登录实现
type TokenLogin struct {
	*BaseLogin
}

// NewTokenLogin 创建Token登录实例
func NewTokenLogin() *TokenLogin {
	return &TokenLogin{
		BaseLogin: NewBaseLogin(commonPB.LOGIN_TYPE_LT_TOKEN),
	}
}

// validateRequest 验证请求参数
func (l *TokenLogin) validateRequest(req *loginRpc.LoginReq) error {
	if err := l.ValidateBaseParams(req); err != nil {
		return err
	}

	if req.GetThirdToken() == "" {
		return errors.New("token must")
	}

	return nil
}

// Login Token登录流程
func (l *TokenLogin) Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &loginRpc.LoginRsp{
		Ret: protox.DefaultResult(),
	}
	rsp.Ret.Desc += "token login failed"

	// 验证请求参数
	if err := l.validateRequest(req); err != nil {
		entry.Error(err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		rsp.Ret.Desc += " : " + err.Error()
		return rsp, err
	}

	productId := req.GetProductId()
	token := req.GetThirdToken()
	playerId, err := dao.DefaultLoginTokenCache.Get(productId, token)
	if err != nil {
		entry.Errorf("get token fail:%+v playerId:%+v", err, playerId)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, err.Error())
		return rsp, err
	}

	dao.DefaultLoginTokenCache.Flush(productId, token)

	// Token登录不走默认流程
	// 执行钩子：登录前
	errCode, err := l.Hooks.BeforeLogin(ctx, playerId, req)
	if err != nil {
		rsp = &loginRpc.LoginRsp{Ret: protox.FillCodeResult(errCode, err.Error())}
		return rsp, err
	}

	rsp, err = l.HandleLogin(ctx, req, playerId)
	if err != nil {
		return rsp, err
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	// 执行钩子：登录后
	errCode, err = l.Hooks.AfterLogin(ctx, req, rsp)
	if err != nil {
		rsp.Ret = protox.FillCodeResult(errCode, err.Error())
		return rsp, err
	}

	entry.Infof("token login success - playerId:%v", playerId)

	return rsp, nil
}
