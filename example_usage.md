# Token 双向映射功能使用示例

## 功能概述

现在 `TokenCache` 支持双向映射：
- `token -> playerID` 映射
- `playerID -> token` 映射

## 新增的方法

### 1. GetTokenByPlayerID - 根据 playerID 获取 token
```go
token, err := dao.DefaultLoginTokenCache.GetTokenByPlayerID(productId, playerID)
if err != nil {
    if errors.Is(err, dao.ErrPlayerTokenNotFound) {
        // 该玩家没有有效的 token
    } else {
        // 其他错误
    }
}
```

### 2. DeleteByPlayerID - 根据 playerID 删除 token
```go
err := dao.DefaultLoginTokenCache.DeleteByPlayerID(productId, playerID)
if err != nil {
    // 处理删除错误
}
```

## 使用场景

### 场景1：用户退出登录
```go
// 在用户退出登录时，可以直接通过 playerID 删除 token
func LogoutByPlayerID(ctx context.Context, productId int32, playerID uint64) error {
    entry := logx.NewLogEntry(ctx)
    
    // 删除该玩家的 token 缓存
    err := dao.DefaultLoginTokenCache.DeleteByPlayerID(productId, playerID)
    if err != nil {
        entry.Errorf("delete token by playerID failed [playerID:%d err: %v]", playerID, err)
        return err
    }
    
    entry.Infof("player logout success [playerID:%d]", playerID)
    return nil
}
```

### 场景2：强制踢出用户
```go
// 强制踢出某个玩家时，删除其 token
func ForceLogout(ctx context.Context, productId int32, playerID uint64) error {
    return dao.DefaultLoginTokenCache.DeleteByPlayerID(productId, playerID)
}
```

### 场景3：检查用户是否在线
```go
// 检查玩家是否有有效的登录 token
func IsPlayerOnline(ctx context.Context, productId int32, playerID uint64) bool {
    _, err := dao.DefaultLoginTokenCache.GetTokenByPlayerID(productId, playerID)
    return err == nil
}
```

## 技术实现特点

1. **原子性操作**：使用 Redis 管道（pipeline）确保双向映射的一致性
2. **简洁设计**：去掉了复杂的接口抽象，保持代码简洁
3. **错误处理**：明确的错误类型，便于业务层处理
4. **向后兼容**：现有的方法保持不变，只是增强了功能

## Redis Key 格式

- Token 映射：`services:token:{productId}:{token}` -> `playerID`
- Player 映射：`services:player_token:{productId}:{playerID}` -> `token`

## 注意事项

1. 所有操作都会同时维护双向映射
2. 删除操作是安全的，即使 key 不存在也不会报错
3. 过期时间统一为 30 天，双向映射的过期时间保持同步
