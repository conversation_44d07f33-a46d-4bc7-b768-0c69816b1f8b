package main

import (
	"context"
	"loginsrv/config"
	"loginsrv/internal/proc"
	"loginsrv/internal/pubsub"
	"loginsrv/internal/repo/record"
	"loginsrv/internal/server/rpc"
	"runtime"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type loginService struct {
	Name string
	Ctx  context.Context
}

func (l *loginService) Init() error {
	l.Ctx = context.Background()
	l.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infoln(l.Name + "服务Init")

	// 初始化配置
	err := config.InitConfig()
	if err != nil {
		logrus.Errorf("初始化配置失败:%+v", err)
		return err
	}

	// 初始化RPC Service
	rpc.InitLoginRpc()

	// 初始化记录模块
	record.Init()

	// 初始化客户端协议
	proc.RegClientMsgHandler()

	// 初始化订阅
	pubsub.InitSubscribe()

	return nil
}

func (l *loginService) Start() error {
	logrus.Infoln(l.Name + "服务启动成功")
	return nil
}

func (l *loginService) Stop() error {
	logrus.Infoln(l.Name + "服务关闭中...")

	// 优雅关闭
	record.Release()
	if err := record.GetProducer().Close(); err != nil {
		logrus.Errorf("close producer failed: %v", err)
	}

	return nil
}

func (l *loginService) ForceStop() error {
	logrus.Infoln(l.Name + " ForceStop ...")
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource()

	driver.Run(&loginService{})
}
