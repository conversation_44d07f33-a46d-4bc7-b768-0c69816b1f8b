package config

import "fmt"

// redisKey
const (
	// RDS_KEY_LOGIN_TOKEN 登录token {productId:token}
	RDS_KEY_LOGIN_TOKEN = "services:token:%d:%s"
	// RDS_KEY_PLAYER_TOKEN 玩家token映射 {productId:playerId}
	RDS_KEY_PLAYER_TOKEN = "services:player_token:%d:%d"
)

const (
	// TOKEN_CACHE_EXPIRE token 过期时间
	TOKEN_CACHE_EXPIRE = 3600 * 24 * 30 // 缓存失效时间改成30天
)

// GetRdsKeyLoginToken 获取登录token缓存key
func GetRdsKeyLoginToken(productId int32, token string) string {
	return fmt.Sprintf(RDS_KEY_LOGIN_TOKEN, productId, token)
}

// GetRdsKeyPlayerToken 获取玩家token映射缓存key
func GetRdsKeyPlayerToken(productId int32, playerID uint64) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_TOKEN, productId, playerID)
}
