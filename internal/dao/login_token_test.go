package dao

import (
	"testing"

	"github.com/gomodule/redigo/redis"
)

func init() {
	DefaultLoginTokenCache = NewLoginTokenSessionCache("192.168.1.54:6379@8888")
}

func TestTokenCache_Create(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		productId int32
		token     string
		playerId  uint64
	}
	var tests []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if err := s.Create(tt.args.productId, tt.args.token, tt.args.playerId); (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestTokenCache_Delete(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		productId int32
		token     string
	}
	var tests []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if err := s.Delete(tt.args.productId, tt.args.token); (err != nil) != tt.wantErr {
				t.Errorf("Delete() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestTokenCache_Flush(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		productId int32
		token     string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if err := s.Flush(tt.args.productId, tt.args.token); (err != nil) != tt.wantErr {
				t.Errorf("Flush() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestTokenCache_Get(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		productId int32
		token     string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint64
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			got, err := s.Get(tt.args.productId, tt.args.token)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Get() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTokenCache_Set(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		productId int32
		playerID  uint64
		token     string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if err := s.Set(tt.args.productId, tt.args.playerID, tt.args.token); (err != nil) != tt.wantErr {
				t.Errorf("Set() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestTokenCache_GetTokenByPlayerID(t *testing.T) {
	// 测试根据playerID获取token
	productId := int32(1)
	playerID := uint64(12345)
	token := "test_token_123"

	// 先设置token
	err := DefaultLoginTokenCache.Set(productId, playerID, token)
	if err != nil {
		t.Errorf("Set token failed: %v", err)
		return
	}

	// 测试获取token
	gotToken, err := DefaultLoginTokenCache.GetTokenByPlayerID(productId, playerID)
	if err != nil {
		t.Errorf("GetTokenByPlayerID() error = %v", err)
		return
	}
	if gotToken != token {
		t.Errorf("GetTokenByPlayerID() = %v, want %v", gotToken, token)
	}

	// 清理
	DefaultLoginTokenCache.DeleteByPlayerID(productId, playerID)
}

func TestTokenCache_DeleteByPlayerID(t *testing.T) {
	// 测试根据playerID删除token
	productId := int32(1)
	playerID := uint64(54321)
	token := "test_token_456"

	// 先设置token
	err := DefaultLoginTokenCache.Set(productId, playerID, token)
	if err != nil {
		t.Errorf("Set token failed: %v", err)
		return
	}

	// 验证token存在
	_, err = DefaultLoginTokenCache.Get(productId, token)
	if err != nil {
		t.Errorf("Token should exist: %v", err)
		return
	}

	// 根据playerID删除token
	err = DefaultLoginTokenCache.DeleteByPlayerID(productId, playerID)
	if err != nil {
		t.Errorf("DeleteByPlayerID() error = %v", err)
		return
	}

	// 验证token已被删除
	_, err = DefaultLoginTokenCache.Get(productId, token)
	if err != ErrTokenNotFound {
		t.Errorf("Token should be deleted, got error: %v", err)
	}

	// 验证player-token映射也被删除
	_, err = DefaultLoginTokenCache.GetTokenByPlayerID(productId, playerID)
	if err != ErrPlayerTokenNotFound {
		t.Errorf("Player token should be deleted, got error: %v", err)
	}
}

func TestTokenCache_getRedisKey(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		productId int32
		token     string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if got := s.getRedisKey(tt.args.productId, tt.args.token); got != tt.want {
				t.Errorf("getRedisKey() = %v, want %v", got, tt.want)
			}
		})
	}
}
