package dao

import (
	"context"
	"errors"
	"loginsrv/config"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// getRedisClient 获取 Redis 客户端
func getRedisClient() *redisx.Client {
	return redisx.GetPlayerCli()
}

var (
	ErrTokenGetFailed      = errors.New("token get failed")
	ErrTokenSetFailed      = errors.New("token set failed")
	ErrTokenExpireFailed   = errors.New("token expire failed")
	ErrTokenNotFound       = errors.New("token not found")
	ErrTokenDelFailed      = errors.New("token del failed")
	ErrPlayerTokenNotFound = errors.New("player token not found")
)

func CreateToken(productId int32, token string, playerID uint64) error {
	ctx := context.Background()
	tokenKey := config.GetRdsKeyLoginToken(productId, token)
	playerTokenKey := config.GetRdsKeyPlayerToken(productId, playerID)

	// 使用管道操作
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.SetEX(ctx, tokenKey, playerID, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)
	pipeLine.SetEX(ctx, playerTokenKey, token, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)

	_, err := pipeLine.Exec(ctx)
	if err != nil {
		return errors.Join(ErrTokenSetFailed, err)
	}

	return nil
}

func GetToken(productId int32, token string) (uint64, error) {
	ctx := context.Background()
	key := config.GetRdsKeyLoginToken(productId, token)

	result := getRedisClient().Get(ctx, key)
	if result.Err() != nil {
		if errors.Is(result.Err(), redis.Nil) {
			return 0, ErrTokenNotFound
		}
		return 0, ErrTokenGetFailed
	}

	playerId, err := result.Uint64()
	if err != nil {
		return 0, ErrTokenGetFailed
	}

	logrus.Debugf("Token: [%d:%s]", playerId, token)
	return playerId, nil
}

func SetToken(productId int32, playerID uint64, token string) error {
	ctx := context.Background()
	tokenKey := config.GetRdsKeyLoginToken(productId, token)
	playerTokenKey := config.GetRdsKeyPlayerToken(productId, playerID)

	// 使用管道操作
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.SetEX(ctx, tokenKey, playerID, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)
	pipeLine.SetEX(ctx, playerTokenKey, token, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)

	_, err := pipeLine.Exec(ctx)
	if err != nil {
		return ErrTokenSetFailed
	}

	return nil
}

func FlushToken(productId int32, token string) error {
	ctx := context.Background()

	// 先获取 playerID
	tokenKey := config.GetRdsKeyLoginToken(productId, token)
	result := getRedisClient().Get(ctx, tokenKey)
	if result.Err() != nil {
		if errors.Is(result.Err(), redis.Nil) {
			return ErrTokenNotFound
		}
		return ErrTokenGetFailed
	}

	playerID, err := result.Uint64()
	if err != nil {
		return ErrTokenGetFailed
	}

	playerTokenKey := config.GetRdsKeyPlayerToken(productId, playerID)

	// 使用管道刷新双向映射的过期时间
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.Expire(ctx, tokenKey, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)
	pipeLine.Expire(ctx, playerTokenKey, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)

	_, err = pipeLine.Exec(ctx)
	if err != nil {
		return ErrTokenExpireFailed
	}

	return nil
}

func DeleteToken(productId int32, token string) error {
	ctx := context.Background()

	// 先获取 playerID
	tokenKey := config.GetRdsKeyLoginToken(productId, token)
	result := getRedisClient().Get(ctx, tokenKey)
	if result.Err() != nil {
		if errors.Is(result.Err(), redis.Nil) {
			return nil // token 不存在
		}
		return ErrTokenGetFailed
	}

	playerID, err := result.Uint64()
	if err != nil {
		return ErrTokenGetFailed
	}

	playerTokenKey := config.GetRdsKeyPlayerToken(productId, playerID)

	// 使用管道删除双向映射
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.Del(ctx, tokenKey)
	pipeLine.Del(ctx, playerTokenKey)

	_, err = pipeLine.Exec(ctx)
	if err != nil {
		return ErrTokenDelFailed
	}

	return nil
}

// GetTokenByPlayerID 根据playerID获取token
func GetTokenByPlayerID(productId int32, playerID uint64) (string, error) {
	ctx := context.Background()
	key := config.GetRdsKeyPlayerToken(productId, playerID)

	result := getRedisClient().Get(ctx, key)
	if result.Err() != nil {
		if errors.Is(result.Err(), redis.Nil) {
			return "", ErrPlayerTokenNotFound
		}
		return "", ErrTokenGetFailed
	}

	token, err := result.Result()
	if err != nil {
		return "", ErrTokenGetFailed
	}

	return token, nil
}

// DeleteTokenByPlayerID 根据playerID删除token缓存
func DeleteTokenByPlayerID(productId int32, playerID uint64) error {
	ctx := context.Background()

	// 先获取 token
	playerTokenKey := config.GetRdsKeyPlayerToken(productId, playerID)
	result := getRedisClient().Get(ctx, playerTokenKey)
	if result.Err() != nil {
		if errors.Is(result.Err(), redis.Nil) {
			return nil // playerID 对应的 token 不存在
		}
		return ErrTokenGetFailed
	}

	token, err := result.Result()
	if err != nil {
		return ErrTokenGetFailed
	}

	tokenKey := config.GetRdsKeyLoginToken(productId, token)

	// 使用管道删除双向映射
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.Del(ctx, tokenKey)
	pipeLine.Del(ctx, playerTokenKey)

	_, err = pipeLine.Exec(ctx)
	if err != nil {
		return ErrTokenDelFailed
	}

	return nil
}
