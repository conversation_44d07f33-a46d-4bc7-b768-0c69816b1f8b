package dao

import (
	"errors"
	"fmt"
	"loginsrv/config"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"

	"strings"
	"time"

	"github.com/gomodule/redigo/redis"
	"github.com/sirupsen/logrus"
)

// getRedisClient 获取 Redis 客户端
func getRedisClient() *redisx.Client {
	return redisx.GetGeneralCli()
}

var DefaultLoginTokenCache *TokenCache

var (
	ErrTokenGetFailed      = errors.New("token get failed")
	ErrTokenSetFailed      = errors.New("token set failed")
	ErrTokenExpireFailed   = errors.New("token expire failed")
	ErrTokenNotFound       = errors.New("token not found")
	ErrTokenDelFailed      = errors.New("token del failed")
	ErrPlayerTokenNotFound = errors.New("player token not found")
)

type TokenCache struct {
	pool *redis.Pool
}

func NewLoginTokenSessionCache(redisSource string) *TokenCache {
	cfg := strings.Split(redisSource, "@")
	if len(cfg) != 2 {
		logrus.Panicf("redis服务地址配置错误")
	}

	pool := &redis.Pool{
		MaxIdle:         300,        // 最多可以保持多少空闲连接, 设置为MaxActive的20%～50%
		MaxActive:       1000 * 1.5, // 最多可以有多少个活跃连接，设置为并发请求的1.5倍左右
		IdleTimeout:     240 * time.Second,
		MaxConnLifetime: 300 * time.Second,
		Dial:            func() (redis.Conn, error) { return redis.Dial("tcp", cfg[0], redis.DialPassword(cfg[1])) },
	}

	return &TokenCache{
		pool: pool,
	}
}

// GetRdsKeyLoginToken 获取登录token缓存key
func GetRdsKeyLoginToken(productId int32, token string) string {
	return fmt.Sprintf(config.RDS_KEY_LOGIN_TOKEN, productId, token)
}

// GetRdsKeyPlayerToken 获取玩家token映射缓存key
func GetRdsKeyPlayerToken(productId int32, playerID uint64) string {
	return fmt.Sprintf(config.RDS_KEY_PLAYER_TOKEN, productId, playerID)
}

func (s *TokenCache) getRedisKey(productId int32, token string) string {
	return GetRdsKeyLoginToken(productId, token)
}

func (s *TokenCache) getPlayerTokenKey(productId int32, playerID uint64) string {
	return GetRdsKeyPlayerToken(productId, playerID)
}

func (s *TokenCache) Create(productId int32, token string, playerID uint64) error {
	c := s.pool.Get()
	defer c.Close()

	tokenKey := s.getRedisKey(productId, token)
	playerTokenKey := s.getPlayerTokenKey(productId, playerID)

	// 使用管道操作
	c.Send(dict.ConfigRedisSETEX, tokenKey, config.TOKEN_CACHE_EXPIRE, playerID)
	c.Send(dict.ConfigRedisSETEX, playerTokenKey, config.TOKEN_CACHE_EXPIRE, token)

	err := c.Flush()
	if err != nil {
		return errors.Join(ErrTokenSetFailed, err)
	}

	return nil
}

func (s *TokenCache) Get(productId int32, token string) (uint64, error) {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(productId, token)
	playerId, err := redis.Uint64(c.Do(dict.ConfigRedisGET, key))
	if err != nil {
		if errors.Is(err, redis.ErrNil) {
			return 0, ErrTokenNotFound
		}
		return 0, ErrTokenGetFailed
	}

	logrus.Debugf("Token: [%d:%s]", playerId, token)
	return playerId, nil
}

func (s *TokenCache) Set(productId int32, playerID uint64, token string) error {
	c := s.pool.Get()
	defer c.Close()

	tokenKey := s.getRedisKey(productId, token)
	playerTokenKey := s.getPlayerTokenKey(productId, playerID)

	// 使用管道操作
	c.Send(dict.ConfigRedisSETEX, tokenKey, config.TOKEN_CACHE_EXPIRE, playerID)
	c.Send(dict.ConfigRedisSETEX, playerTokenKey, config.TOKEN_CACHE_EXPIRE, token)

	err := c.Flush()
	if err != nil {
		return ErrTokenSetFailed
	}

	return nil
}

func (s *TokenCache) Flush(productId int32, token string) error {
	c := s.pool.Get()
	defer c.Close()

	// 先获取 playerID
	tokenKey := s.getRedisKey(productId, token)
	playerID, err := redis.Uint64(c.Do(dict.ConfigRedisGET, tokenKey))
	if err != nil {
		if errors.Is(err, redis.ErrNil) {
			return ErrTokenNotFound
		}
		return ErrTokenGetFailed
	}

	playerTokenKey := s.getPlayerTokenKey(productId, playerID)

	// 使用管道刷新双向映射的过期时间
	c.Send(dict.ConfigRedisEXPIRE, tokenKey, config.TOKEN_CACHE_EXPIRE)
	c.Send(dict.ConfigRedisEXPIRE, playerTokenKey, config.TOKEN_CACHE_EXPIRE)

	err = c.Flush()
	if err != nil {
		return ErrTokenExpireFailed
	}

	return nil
}

func (s *TokenCache) Delete(productId int32, token string) error {
	c := s.pool.Get()
	defer c.Close()

	// 先获取 playerID
	tokenKey := s.getRedisKey(productId, token)
	playerID, err := redis.Uint64(c.Do(dict.ConfigRedisGET, tokenKey))
	if err != nil {
		if errors.Is(err, redis.ErrNil) {
			return nil // token 不存在
		}
		return ErrTokenGetFailed
	}

	playerTokenKey := s.getPlayerTokenKey(productId, playerID)

	// 使用管道删除双向映射
	c.Send(dict.ConfigRedisDEL, tokenKey)
	c.Send(dict.ConfigRedisDEL, playerTokenKey)

	err = c.Flush()
	if err != nil {
		return ErrTokenDelFailed
	}

	return nil
}

// GetTokenByPlayerID 根据playerID获取token
func (s *TokenCache) GetTokenByPlayerID(productId int32, playerID uint64) (string, error) {
	c := s.pool.Get()
	defer c.Close()

	key := s.getPlayerTokenKey(productId, playerID)
	token, err := redis.String(c.Do(dict.ConfigRedisGET, key))
	if err != nil {
		if errors.Is(err, redis.ErrNil) {
			return "", ErrPlayerTokenNotFound
		}
		return "", ErrTokenGetFailed
	}

	return token, nil
}

// DeleteByPlayerID 根据playerID删除token缓存
func (s *TokenCache) DeleteByPlayerID(productId int32, playerID uint64) error {
	c := s.pool.Get()
	defer c.Close()

	// 先获取 token
	playerTokenKey := s.getPlayerTokenKey(productId, playerID)
	token, err := redis.String(c.Do(dict.ConfigRedisGET, playerTokenKey))
	if err != nil {
		if errors.Is(err, redis.ErrNil) {
			return nil // playerID 对应的 token 不存在
		}
		return ErrTokenGetFailed
	}

	tokenKey := s.getRedisKey(productId, token)

	// 使用管道删除双向映射
	c.Send(dict.ConfigRedisDEL, tokenKey)
	c.Send(dict.ConfigRedisDEL, playerTokenKey)

	err = c.Flush()
	if err != nil {
		return ErrTokenDelFailed
	}

	return nil
}

func InitTokenRedisInstance() {
	playRedis := redisfactory.DefaultFactory.GetRedisConfig(dict_redis.RDBPlayer)
	redisSource := playRedis.Address + "@" + playRedis.Passwd
	DefaultLoginTokenCache = NewLoginTokenSessionCache(redisSource)
}
