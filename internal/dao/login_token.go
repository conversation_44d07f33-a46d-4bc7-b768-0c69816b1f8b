package dao

import (
	"errors"
	"fmt"
	"loginsrv/config"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"

	"strings"
	"time"

	"github.com/gomodule/redigo/redis"
	"github.com/sirupsen/logrus"
)

// ITokenCache Token缓存接口
type ITokenCache interface {
	// Create 创建token缓存
	Create(productId int32, token string, playerID uint64) error
	// Get 根据token获取playerID
	Get(productId int32, token string) (uint64, error)
	// Set 设置token缓存
	Set(productId int32, playerID uint64, token string) error
	// Flush 刷新token过期时间
	Flush(productId int32, token string) error
	// Delete 删除token缓存
	Delete(productId int32, token string) error
	// GetTokenByPlayerID 根据playerID获取token
	GetTokenByPlayerID(productId int32, playerID uint64) (string, error)
	// DeleteByPlayerID 根据playerID删除token缓存
	DeleteByPlayerID(productId int32, playerID uint64) error
}

var DefaultLoginTokenCache ITokenCache

var (
	ErrTokenGetFailed      = errors.New("token get failed")
	ErrTokenSetFailed      = errors.New("token set failed")
	ErrTokenExpireFailed   = errors.New("token expire failed")
	ErrTokenNotFound       = errors.New("token not found")
	ErrTokenDelFailed      = errors.New("token del failed")
	ErrPlayerTokenNotFound = errors.New("player token not found")
	ErrPipelineFailed      = errors.New("redis pipeline failed")
)

type TokenCache struct {
	pool *redis.Pool
}

func NewLoginTokenSessionCache(redisSource string) *TokenCache {
	cfg := strings.Split(redisSource, "@")
	if len(cfg) != 2 {
		logrus.Panicf("redis服务地址配置错误")
	}

	pool := &redis.Pool{
		MaxIdle:         300,        // 最多可以保持多少空闲连接, 设置为MaxActive的20%～50%
		MaxActive:       1000 * 1.5, // 最多可以有多少个活跃连接，设置为并发请求的1.5倍左右
		IdleTimeout:     240 * time.Second,
		MaxConnLifetime: 300 * time.Second,
		Dial:            func() (redis.Conn, error) { return redis.Dial("tcp", cfg[0], redis.DialPassword(cfg[1])) },
	}

	return &TokenCache{
		pool: pool,
	}
}

func (s *TokenCache) getRedisKey(productId int32, token string) string {
	return fmt.Sprintf(config.RDS_KEY_LOGIN_TOKEN, productId, token)
}

func (s *TokenCache) getPlayerTokenKey(productId int32, playerID uint64) string {
	return fmt.Sprintf(config.RDS_KEY_PLAYER_TOKEN, productId, playerID)
}

func (s *TokenCache) Create(productId int32, token string, playerID uint64) error {
	c := s.pool.Get()
	defer c.Close()

	tokenKey := s.getRedisKey(productId, token)
	playerTokenKey := s.getPlayerTokenKey(productId, playerID)

	// 使用管道操作保证原子性
	err := c.Send("MULTI")
	if err != nil {
		return fmt.Errorf("%w: failed to start transaction: %v", ErrPipelineFailed, err)
	}

	// 设置 token -> playerID 映射
	err = c.Send(dict.ConfigRedisSETEX, tokenKey, config.TOKEN_CACHE_EXPIRE, playerID)
	if err != nil {
		c.Send("DISCARD")
		return fmt.Errorf("%w: failed to set token: %v", ErrTokenSetFailed, err)
	}

	// 设置 playerID -> token 映射
	err = c.Send(dict.ConfigRedisSETEX, playerTokenKey, config.TOKEN_CACHE_EXPIRE, token)
	if err != nil {
		c.Send("DISCARD")
		return fmt.Errorf("%w: failed to set player token: %v", ErrTokenSetFailed, err)
	}

	// 执行事务
	_, err = c.Do("EXEC")
	if err != nil {
		return fmt.Errorf("%w: failed to execute transaction: %v", ErrPipelineFailed, err)
	}

	logrus.Debugf("Created token mapping: [%d:%s] -> %d", productId, token, playerID)
	return nil
}

func (s *TokenCache) Get(productId int32, token string) (uint64, error) {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(productId, token)
	playerId, err := redis.Uint64(c.Do(dict.ConfigRedisGET, key))
	if err != nil {
		if errors.Is(err, redis.ErrNil) {
			return 0, ErrTokenNotFound
		}
		return 0, ErrTokenGetFailed
	}

	logrus.Debugf("Token: [%d:%s]", playerId, token)
	return playerId, nil
}

func (s *TokenCache) Set(productId int32, playerID uint64, token string) error {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(productId, token)
	err := c.Send(dict.ConfigRedisSETEX, key, config.TOKEN_CACHE_EXPIRE, playerID)
	if err != nil {
		return ErrTokenSetFailed
	}

	return nil
}

func (s *TokenCache) Flush(productId int32, token string) error {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(productId, token)
	err := c.Send(dict.ConfigRedisEXPIRE, key, config.TOKEN_CACHE_EXPIRE)
	if err != nil {
		return ErrTokenExpireFailed
	}

	return nil
}

func (s *TokenCache) Delete(productId int32, token string) error {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(productId, token)
	err := c.Send(dict.ConfigRedisDEL, key)
	if err != nil {
		return ErrTokenDelFailed
	}

	return nil
}

func InitTokenRedisInstance() {
	playRedis := redisfactory.DefaultFactory.GetRedisConfig(dict_redis.RDBPlayer)
	redisSource := playRedis.Address + "@" + playRedis.Passwd
	DefaultLoginTokenCache = NewLoginTokenSessionCache(redisSource)
}
