package dao

import (
	"context"
	"errors"
	"fmt"
	"loginsrv/config"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/sirupsen/logrus"
)

// getRedisClient 获取 Redis 客户端
func getRedisClient() *redisx.Client {
	return redisx.GetPlayerCli()
}

var DefaultLoginTokenCache *TokenCache

var (
	ErrTokenGetFailed      = errors.New("token get failed")
	ErrTokenSetFailed      = errors.New("token set failed")
	ErrTokenExpireFailed   = errors.New("token expire failed")
	ErrTokenNotFound       = errors.New("token not found")
	ErrTokenDelFailed      = errors.New("token del failed")
	ErrPlayerTokenNotFound = errors.New("player token not found")
)

type TokenCache struct {
}

func NewLoginTokenSessionCache() *TokenCache {
	return &TokenCache{}
}

// GetRdsKeyLoginToken 获取登录token缓存key
func GetRdsKeyLoginToken(productId int32, token string) string {
	return fmt.Sprintf(config.RDS_KEY_LOGIN_TOKEN, productId, token)
}

// GetRdsKeyPlayerToken 获取玩家token映射缓存key
func GetRdsKeyPlayerToken(productId int32, playerID uint64) string {
	return fmt.Sprintf(config.RDS_KEY_PLAYER_TOKEN, productId, playerID)
}

func (s *TokenCache) getRedisKey(productId int32, token string) string {
	return GetRdsKeyLoginToken(productId, token)
}

func (s *TokenCache) getPlayerTokenKey(productId int32, playerID uint64) string {
	return GetRdsKeyPlayerToken(productId, playerID)
}

func (s *TokenCache) Create(productId int32, token string, playerID uint64) error {
	ctx := context.Background()
	tokenKey := s.getRedisKey(productId, token)
	playerTokenKey := s.getPlayerTokenKey(productId, playerID)

	// 使用管道操作
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.SetEX(ctx, tokenKey, playerID, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)
	pipeLine.SetEX(ctx, playerTokenKey, token, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)

	_, err := pipeLine.Exec(ctx)
	if err != nil {
		return errors.Join(ErrTokenSetFailed, err)
	}

	return nil
}

func (s *TokenCache) Get(productId int32, token string) (uint64, error) {
	ctx := context.Background()
	key := s.getRedisKey(productId, token)

	result := getRedisClient().Get(ctx, key)
	if result.Err() != nil {
		if result.Err().Error() == "redis: nil" {
			return 0, ErrTokenNotFound
		}
		return 0, ErrTokenGetFailed
	}

	playerId, err := result.Uint64()
	if err != nil {
		return 0, ErrTokenGetFailed
	}

	logrus.Debugf("Token: [%d:%s]", playerId, token)
	return playerId, nil
}

func (s *TokenCache) Set(productId int32, playerID uint64, token string) error {
	ctx := context.Background()
	tokenKey := s.getRedisKey(productId, token)
	playerTokenKey := s.getPlayerTokenKey(productId, playerID)

	// 使用管道操作
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.SetEX(ctx, tokenKey, playerID, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)
	pipeLine.SetEX(ctx, playerTokenKey, token, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)

	_, err := pipeLine.Exec(ctx)
	if err != nil {
		return ErrTokenSetFailed
	}

	return nil
}

func (s *TokenCache) Flush(productId int32, token string) error {
	ctx := context.Background()

	// 先获取 playerID
	tokenKey := s.getRedisKey(productId, token)
	result := getRedisClient().Get(ctx, tokenKey)
	if result.Err() != nil {
		if result.Err().Error() == "redis: nil" {
			return ErrTokenNotFound
		}
		return ErrTokenGetFailed
	}

	playerID, err := result.Uint64()
	if err != nil {
		return ErrTokenGetFailed
	}

	playerTokenKey := s.getPlayerTokenKey(productId, playerID)

	// 使用管道刷新双向映射的过期时间
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.Expire(ctx, tokenKey, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)
	pipeLine.Expire(ctx, playerTokenKey, time.Duration(config.TOKEN_CACHE_EXPIRE)*time.Second)

	_, err = pipeLine.Exec(ctx)
	if err != nil {
		return ErrTokenExpireFailed
	}

	return nil
}

func (s *TokenCache) Delete(productId int32, token string) error {
	ctx := context.Background()

	// 先获取 playerID
	tokenKey := s.getRedisKey(productId, token)
	result := getRedisClient().Get(ctx, tokenKey)
	if result.Err() != nil {
		if result.Err().Error() == "redis: nil" {
			return nil // token 不存在
		}
		return ErrTokenGetFailed
	}

	playerID, err := result.Uint64()
	if err != nil {
		return ErrTokenGetFailed
	}

	playerTokenKey := s.getPlayerTokenKey(productId, playerID)

	// 使用管道删除双向映射
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.Del(ctx, tokenKey)
	pipeLine.Del(ctx, playerTokenKey)

	_, err = pipeLine.Exec(ctx)
	if err != nil {
		return ErrTokenDelFailed
	}

	return nil
}

// GetTokenByPlayerID 根据playerID获取token
func (s *TokenCache) GetTokenByPlayerID(productId int32, playerID uint64) (string, error) {
	ctx := context.Background()
	key := s.getPlayerTokenKey(productId, playerID)

	result := getRedisClient().Get(ctx, key)
	if result.Err() != nil {
		if result.Err().Error() == "redis: nil" {
			return "", ErrPlayerTokenNotFound
		}
		return "", ErrTokenGetFailed
	}

	token, err := result.Result()
	if err != nil {
		return "", ErrTokenGetFailed
	}

	return token, nil
}

// DeleteByPlayerID 根据playerID删除token缓存
func (s *TokenCache) DeleteByPlayerID(productId int32, playerID uint64) error {
	ctx := context.Background()

	// 先获取 token
	playerTokenKey := s.getPlayerTokenKey(productId, playerID)
	result := getRedisClient().Get(ctx, playerTokenKey)
	if result.Err() != nil {
		if result.Err().Error() == "redis: nil" {
			return nil // playerID 对应的 token 不存在
		}
		return ErrTokenGetFailed
	}

	token, err := result.Result()
	if err != nil {
		return ErrTokenGetFailed
	}

	tokenKey := s.getRedisKey(productId, token)

	// 使用管道删除双向映射
	pipeLine := getRedisClient().TxPipeline()
	pipeLine.Del(ctx, tokenKey)
	pipeLine.Del(ctx, playerTokenKey)

	_, err = pipeLine.Exec(ctx)
	if err != nil {
		return ErrTokenDelFailed
	}

	return nil
}

func InitTokenRedisInstance() {
	DefaultLoginTokenCache = NewLoginTokenSessionCache()
}
